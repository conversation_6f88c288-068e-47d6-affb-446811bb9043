import { useCallback, useRef } from "react";
import { Canvas } from "fabric";
import { createSave<PERSON><PERSON><PERSON> } from "@/lib/fabric/operations";
import { setupCanvasEventListeners } from "@/lib/fabric/events";
import { CropData, ImageViewerProps, FabricObjectState, FilterParams } from "@/shared/types";
import { setupImageCanvas } from "@/lib/fabric/canvas";
import { useUndoTracking } from "./useUndoTracking";
import { useFilterManagement } from "./useFilterManagement";
import { useImageTransforms } from "./useImageTransforms";
import { useCropManagement } from "./useCropManagement";

/*
Composes Fabric viewer state: canvas setup, filters, transforms, crop, undo,
plus an imperative setupCanvas and save handler. Designed for both Image/Stack.
*/
export const useFabricViewer = ({
  data,
  containerRef,
  onResizeNeeded,
}: ImageViewerProps & {
  containerRef?: React.RefObject<HTMLElement | null>;
  onResizeNeeded?: () => void;
}) => {
  const fabricCanvas = useRef<Canvas | null>(null);
  const eventDisposers = useRef<(() => void)[]>([]);
  const initialObjectCount = useRef(0);
  const originalImageUrl = useRef("");
  const objectStates = useRef(new Map<string, FabricObjectState>());

  const undoTracking = useUndoTracking(fabricCanvas, initialObjectCount);

  const filterManagement = useFilterManagement(data.viewer.fabricConfigs, fabricCanvas);

  const imageTransforms = useImageTransforms(
    fabricCanvas,
    data.viewer.fabricConfigs.transformState!,
    onResizeNeeded
  );

  const cropManagement = useCropManagement(
    fabricCanvas,
    data.viewer.fabricConfigs.cropData!,
    undoTracking,
    containerRef,
    originalImageUrl.current,
    imageTransforms.transformState
  );
  const isInitializingRef = useRef(false);

  // Initializes or re-initializes the Fabric canvas for the given image
  const setupCanvas = useCallback(
    async (canvasElement: HTMLCanvasElement, imageSource: string) => {
      if (isInitializingRef.current) return;
      isInitializingRef.current = true;

      const result = await setupImageCanvas({
        canvasElement,
        imageUrl: imageSource,
        annotations: data.viewer.fabricConfigs.annotations,
        filters: filterManagement.filters as FilterParams,
        cropData: data.viewer.fabricConfigs.cropData as CropData,
        existingCanvas: fabricCanvas.current,
        transformState: data.viewer.fabricConfigs.transformState,
      });

      fabricCanvas.current = result.canvas;
      originalImageUrl.current = imageSource;

      if (data.viewer.fabricConfigs.cropData) {
        const loadedCropData = data.viewer.fabricConfigs.cropData as CropData;
        if (loadedCropData.isCropped !== cropManagement.cropData.isCropped) {
          cropManagement.setCropData(loadedCropData);
          cropManagement.setHasPerformedCrop(loadedCropData.isCropped);
        }
      }
      const canvas = fabricCanvas.current;

      (canvas as any).annotationColor = sessionStorage.getItem("annotationColor") || "#ff0000";

      initialObjectCount.current = canvas.getObjects().length;

      // Rewire Fabric event listeners so handlers reflect current closures/state
      eventDisposers.current.forEach((dispose) => dispose());
      eventDisposers.current = setupCanvasEventListeners(canvas, undoTracking, objectStates);

      isInitializingRef.current = false;
    },
    [
      data.viewer.fabricConfigs.annotations,
      data.viewer.fabricConfigs.cropData,
      data.viewer.fabricConfigs.transformState,
      filterManagement.filters,
      undoTracking,
      cropManagement,
    ]
  );

  const handleSave = createSaveHandler(
    fabricCanvas,
    data.id,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState
  );

  return {
    canvas: fabricCanvas,
    setupCanvas,
    ...filterManagement.filters,
    ...filterManagement.filterHandlers,
    ...imageTransforms,
    ...undoTracking,
    ...cropManagement,
    handleSave,
  };
};
